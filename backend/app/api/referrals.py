from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel
from decimal import Decimal
from datetime import datetime, timedelta
import secrets
import string
from typing import List, Optional

from app.db.session import get_db
from app.models.user import User
from app.models.referral_credit import ReferralCredit
from app.api.auth import get_current_user, get_admin_user
from app.core.config import get_settings

router = APIRouter()

class ReferralLinkResponse(BaseModel):
    referral_code: str
    referral_url: str

class ReferralStatsResponse(BaseModel):
    referral_code: str
    total_referrals: int
    total_credits_earned: float
    active_credits: float
    expired_credits: float

class ReferralCreditResponse(BaseModel):
    id: int
    amount: float
    credit_type: str
    expires_at: Optional[datetime]
    used_amount: float
    remaining_amount: float
    is_active: bool
    is_expired: bool
    is_usable: bool
    created_at: datetime
    ip_address: Optional[str]

class AdminReferralCreditResponse(BaseModel):
    id: int
    amount: float
    credit_type: str
    expires_at: Optional[datetime]
    used_amount: float
    remaining_amount: float
    is_active: bool
    is_expired: bool
    is_usable: bool
    created_at: datetime
    ip_address: Optional[str]
    user_id: int
    user_email: str
    user_username: str
    referral_user_id: Optional[int]
    referral_user_email: Optional[str]
    referral_user_username: Optional[str]

class PaginatedAdminReferralCredits(BaseModel):
    credits: List[AdminReferralCreditResponse]
    total_pages: int
    current_page: int
    has_next: bool
    has_previous: bool

class ReferralCreditAdjustment(BaseModel):
    amount: float

def generate_referral_code() -> str:
    """Generate a unique referral code."""
    # Generate a 8-character alphanumeric code
    alphabet = string.ascii_uppercase + string.digits
    # Exclude confusing characters
    alphabet = alphabet.replace('0', '').replace('O', '').replace('1', '').replace('I', '')
    return ''.join(secrets.choice(alphabet) for _ in range(8))

@router.get("/my-link", response_model=ReferralLinkResponse)
def get_my_referral_link(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get or create the current user's referral link.
    Requires user to have minimum balance to prevent referral abuse.
    """
    settings = get_settings()

    # Check if user has sufficient balance to refer others
    # This prevents users from creating referral loops without adding real funds
    from app.services.billing_service import BillingService
    billing_service = BillingService(db)
    balance_info = billing_service.get_user_total_balance(current_user)

    if balance_info["total_balance"] < Decimal(str(settings.MIN_BALANCE_TO_DEPLOY)):
        raise HTTPException(
            status_code=402,
            detail=f"Insufficient balance to create referrals. Minimum {settings.MIN_BALANCE_TO_DEPLOY} EUR required. "
                   f"Current balance: {balance_info['regular_balance']:.2f} EUR + "
                   f"{balance_info['referral_credits']:.2f} EUR referral credits + "
                   f"{balance_info['signup_credits']:.2f} EUR signup credits = "
                   f"{balance_info['total_balance']:.2f} EUR total. "
                   f"Please add funds to your account to refer others."
        )

    # Check if user already has a referral code
    if not current_user.referral_code:
        # Generate a unique referral code
        while True:
            code = generate_referral_code()
            # Check if code already exists
            existing = db.query(User).filter(User.referral_code == code).first()
            if not existing:
                current_user.referral_code = code
                db.add(current_user)
                db.commit()
                db.refresh(current_user)
                break
    
    # Create referral URL using configurable base URL
    settings = get_settings()
    referral_url = f"{settings.REFERRAL_BASE_URL}/signup?ref={current_user.referral_code}"
    
    return ReferralLinkResponse(
        referral_code=current_user.referral_code,
        referral_url=referral_url
    )

@router.get("/stats", response_model=ReferralStatsResponse)
def get_referral_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get referral statistics for the current user.
    """
    if not current_user.referral_code:
        return ReferralStatsResponse(
            referral_code="",
            total_referrals=0,
            total_credits_earned=0.0,
            active_credits=0.0,
            expired_credits=0.0
        )
    
    # Count total referrals
    total_referrals = db.query(User).filter(User.referred_by_id == current_user.id).count()
    
    # Get referral credits earned by this user (as referrer)
    referrer_credits = db.query(ReferralCredit).filter(
        ReferralCredit.user_id == current_user.id,
        ReferralCredit.credit_type == "referrer"
    ).all()
    
    total_credits_earned = sum(float(credit.amount) for credit in referrer_credits)
    active_credits = sum(float(credit.remaining_amount) for credit in referrer_credits if credit.is_usable)
    expired_credits = sum(float(credit.remaining_amount) for credit in referrer_credits if credit.is_expired)
    
    return ReferralStatsResponse(
        referral_code=current_user.referral_code,
        total_referrals=total_referrals,
        total_credits_earned=total_credits_earned,
        active_credits=active_credits,
        expired_credits=expired_credits
    )

@router.get("/credits", response_model=List[ReferralCreditResponse])
def get_my_referral_credits(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all referral credits for the current user.
    """
    credits = db.query(ReferralCredit).filter(
        ReferralCredit.user_id == current_user.id
    ).order_by(ReferralCredit.created_at.desc()).all()
    
    return [
        ReferralCreditResponse(
            id=credit.id,
            amount=float(credit.amount),
            credit_type=credit.credit_type,
            expires_at=credit.expires_at,
            used_amount=float(credit.used_amount),
            remaining_amount=float(credit.remaining_amount),
            is_active=credit.is_active,
            is_expired=credit.is_expired,
            is_usable=credit.is_usable,
            created_at=credit.created_at,
            ip_address=credit.ip_address
        )
        for credit in credits
    ]

@router.get("/validate-code/{referral_code}")
def validate_referral_code(
    referral_code: str,
    db: Session = Depends(get_db)
):
    """
    Validate if a referral code exists and is valid.
    This is a public endpoint for the signup form.
    """
    user = db.query(User).filter(User.referral_code == referral_code).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Invalid referral code")
    
    return {
        "valid": True,
        "referrer_username": user.username,
        "message": f"Valid referral code from {user.username}"
    }

# Admin endpoints
@router.get("/admin/credits", response_model=PaginatedAdminReferralCredits)
def get_admin_referral_credits(
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    per_page: int = Query(50, ge=1, le=200, description="Items per page"),
    sort_by: str = Query("created_at", description="Sort field: created_at, amount, ip_address, user_email"),
    sort_order: str = Query("desc", description="Sort order: asc or desc"),
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    """
    Get all referral credits with user information for admin view.
    """
    from sqlalchemy import desc, asc

    # Build query with joins
    query = db.query(ReferralCredit).join(User, ReferralCredit.user_id == User.id)

    # Apply sorting
    sort_func = desc if sort_order.lower() == "desc" else asc
    if sort_by == "created_at":
        query = query.order_by(sort_func(ReferralCredit.created_at))
    elif sort_by == "amount":
        query = query.order_by(sort_func(ReferralCredit.amount))
    elif sort_by == "ip_address":
        query = query.order_by(sort_func(ReferralCredit.ip_address))
    elif sort_by == "user_email":
        query = query.order_by(sort_func(User.email))
    else:
        query = query.order_by(desc(ReferralCredit.created_at))

    # Get total count for pagination
    total_count = query.count()
    total_pages = max(1, (total_count + per_page - 1) // per_page)

    # Apply pagination
    offset = (page - 1) * per_page
    credits = query.offset(offset).limit(per_page).all()

    # Build response with user information
    credit_responses = []
    for credit in credits:
        # Get referral user info if exists
        referral_user = None
        if credit.referral_user_id:
            referral_user = db.query(User).filter(User.id == credit.referral_user_id).first()

        credit_responses.append(AdminReferralCreditResponse(
            id=credit.id,
            amount=float(credit.amount),
            credit_type=credit.credit_type,
            expires_at=credit.expires_at,
            used_amount=float(credit.used_amount),
            remaining_amount=float(credit.remaining_amount),
            is_active=credit.is_active,
            is_expired=credit.is_expired,
            is_usable=credit.is_usable,
            created_at=credit.created_at,
            ip_address=credit.ip_address,
            user_id=credit.user.id,
            user_email=credit.user.email,
            user_username=credit.user.username,
            referral_user_id=referral_user.id if referral_user else None,
            referral_user_email=referral_user.email if referral_user else None,
            referral_user_username=referral_user.username if referral_user else None
        ))

    return PaginatedAdminReferralCredits(
        credits=credit_responses,
        total_pages=total_pages,
        current_page=page,
        has_next=page < total_pages,
        has_previous=page > 1
    )

@router.patch("/admin/credits/{credit_id}/adjust", response_model=AdminReferralCreditResponse)
def adjust_referral_credit(
    credit_id: int,
    adjustment: ReferralCreditAdjustment,
    db: Session = Depends(get_db),
    admin_user: User = Depends(get_admin_user)
):
    """
    Adjust the amount of a referral credit. Admin only.
    """
    credit = db.query(ReferralCredit).filter(ReferralCredit.id == credit_id).first()
    if not credit:
        raise HTTPException(status_code=404, detail="Referral credit not found")

    # Update the credit amount
    credit.amount = Decimal(str(adjustment.amount))

    # Create a transaction record for the adjustment
    from app.models.transaction import Transaction
    transaction = Transaction(
        user_id=credit.user_id,
        amount=Decimal(str(adjustment.amount)) - credit.amount,  # Difference
        type="referral_credit_adjustment",
        description=f"Admin adjustment of referral credit {credit_id} by {admin_user.username}"
    )

    db.add(credit)
    db.add(transaction)
    db.commit()
    db.refresh(credit)

    # Get user info for response
    user = db.query(User).filter(User.id == credit.user_id).first()
    referral_user = None
    if credit.referral_user_id:
        referral_user = db.query(User).filter(User.id == credit.referral_user_id).first()

    return AdminReferralCreditResponse(
        id=credit.id,
        amount=float(credit.amount),
        credit_type=credit.credit_type,
        expires_at=credit.expires_at,
        used_amount=float(credit.used_amount),
        remaining_amount=float(credit.remaining_amount),
        is_active=credit.is_active,
        is_expired=credit.is_expired,
        is_usable=credit.is_usable,
        created_at=credit.created_at,
        ip_address=credit.ip_address,
        user_id=user.id,
        user_email=user.email,
        user_username=user.username,
        referral_user_id=referral_user.id if referral_user else None,
        referral_user_email=referral_user.email if referral_user else None,
        referral_user_username=referral_user.username if referral_user else None
    )
