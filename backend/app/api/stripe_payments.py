from fastapi import APIRouter, Depends, HTTPException, Request, Response
from sqlalchemy.orm import Session
from pydantic import BaseModel
from decimal import Decimal
import stripe
import os
import time
from typing import Dict

from app.db.session import get_db
from app.models.user import User
from app.models.transaction import Transaction
from app.api.auth import get_current_user
from app.core.config import get_settings

router = APIRouter()

class CheckoutSessionRequest(BaseModel):
    amount_cents: int

class CheckoutSessionResponse(BaseModel):
    url: str

# Predefined mapping of valid credit amounts to Stripe Price IDs
# This prevents users from creating arbitrary amounts
# These are your sandbox Stripe Price IDs from your dashboard
#PRICE_MAP: Dict[int, str] = {
#    500: "price_1S7AfBQAhsU9wYQVsKfkifKp",   # $5.00
#    1000: "price_1S7AfSQAhsU9wYQVlE3fBDmX",   # $10.00
#    2000: "price_1S7AfiQAhsU9wYQVIh0SXcwC",   # $20.00
#    5000: "price_1S7AgEQAhsU9wYQVQL1PqviO",   # $50.00
#    10000: "price_1S7AgXQAhsU9wYQV9KDoJN2m",   # $100.00
#}

# These are your actual Stripe Price IDs from your dashboard
PRICE_MAP: Dict[int, str] = {
    500: "price_1S79xr4OBvbk4LEq94xFQCme",   # $5.00
    1000: "price_1S79xF4OBvbk4LEq5mWN8Hg9",   # $10.00
    2000: "price_1S79yA4OBvbk4LEq8ZiRexhV",   # $20.00
    5000: "price_1S79yT4OBvbk4LEqcs0aPRpk",   # $50.00
    10000: "price_1S79ys4OBvbk4LEq6aXLlYze",   # $100.00
}


@router.post("/create-checkout-session", response_model=CheckoutSessionResponse)
async def create_checkout_session(
    request: CheckoutSessionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a Stripe Checkout Session for user top-up.
    """
    settings = get_settings()

    # Validate amount against predefined mapping
    if request.amount_cents not in PRICE_MAP:
        valid_amounts = list(PRICE_MAP.keys())
        raise HTTPException(
            status_code=400,
            detail=f"Invalid amount. Valid amounts are: {', '.join([f'{amt/100:.2f}' for amt in PRICE_MAP.keys()])} dollars"
        )

    # For testing without Stripe configuration, return a mock URL
    if not settings.STRIPE_SECRET_KEY:
        mock_session_id = f"cs_test_{current_user.id}_{request.amount_cents}"
        return CheckoutSessionResponse(
            url=f"{settings.FRONTEND_BASE_URL}/success?session_id={mock_session_id}&test_mode=true"
        )

    # Set Stripe API key
    stripe.api_key = settings.STRIPE_SECRET_KEY

    try:
        # Create Stripe Checkout Session
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[{
                'price': PRICE_MAP[request.amount_cents],
                'quantity': 1,
            }],
            mode='payment',
            success_url=f'{settings.FRONTEND_BASE_URL}/success?session_id={{CHECKOUT_SESSION_ID}}',
            cancel_url=f'{settings.FRONTEND_BASE_URL}/cancel',
            metadata={
                'user_id': str(current_user.id),
                'amount_cents': str(request.amount_cents)
            }
        )

        return CheckoutSessionResponse(url=checkout_session.url)

    except stripe.error.StripeError as e:
        raise HTTPException(status_code=400, detail=f"Stripe error: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")

@router.post("/TBD_test-webhook")
async def TBD_test_stripe_webhook(
    user_id: int,
    amount_cents: int,
    db: Session = Depends(get_db)
):
    """
    TBD_: Test endpoint to simulate a successful Stripe payment webhook.
    This is for testing purposes only and should be removed in production.
    """
    # Get the user from database
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Convert amount from cents to USD
    amount_usd = Decimal(amount_cents) / 100

    # Update user balance
    user.balance = (user.balance or Decimal("0")) + amount_usd

    # Create transaction record
    transaction = Transaction(
        user_id=user.id,
        amount=amount_usd,
        type="stripe_top_up",
        description=f"Test Stripe payment - ${amount_usd}"
    )

    db.add(user)
    db.add(transaction)
    db.commit()

    return {
        "message": f"Test payment processed for user {user_id}: ${amount_usd}",
        "new_balance": float(user.balance)
    }

@router.post("/webhook")
async def stripe_webhook(
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Handle Stripe webhook events, specifically checkout.session.completed.
    """
    settings = get_settings()

    # Get the raw body and signature
    payload = await request.body()
    sig_header = request.headers.get('stripe-signature')

    # For testing purposes, allow bypassing signature verification
    # In production, always verify the signature
    if settings.STRIPE_WEBHOOK_SECRET and sig_header:
        try:
            # Verify webhook signature
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
        except ValueError:
            # Invalid payload
            raise HTTPException(status_code=400, detail="Invalid payload")
        except stripe.error.SignatureVerificationError:
            # Invalid signature
            raise HTTPException(status_code=400, detail="Invalid signature")
    else:
        # For testing without signature verification
        try:
            event = await request.json()
        except Exception:
            raise HTTPException(status_code=400, detail="Invalid JSON payload")
    
    # Handle the checkout.session.completed event
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        
        # Extract user_id from metadata
        user_id = session['metadata'].get('user_id')
        if not user_id:
            raise HTTPException(status_code=400, detail="Missing user_id in session metadata")
        
        try:
            user_id = int(user_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid user_id in session metadata")
        
        # Get the user from database
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get amount from session (amount is in cents)
        amount_total = session['amount_total']  # This is in cents
        amount_usd = Decimal(amount_total) / 100  # Convert to USD

        # Update user balance
        user.balance = (user.balance or Decimal("0")) + amount_usd

        # Create transaction record
        transaction = Transaction(
            user_id=user.id,
            amount=amount_usd,
            type="stripe_top_up",
            description=f"Stripe payment - ${amount_usd}"
        )

        db.add(user)
        db.add(transaction)
        db.commit()

        print(f"Successfully processed Stripe payment for user {user_id}: ${amount_usd}")
    
    return Response(status_code=200)

class TBD_TestWebhookRequest(BaseModel):
    user_id: int
    amount_cents: int
    session_id: str = "cs_test_mock_session"

@router.post("/TBD_test-webhook-v2")
async def TBD_test_stripe_webhook_v2(
    request: TBD_TestWebhookRequest,
    db: Session = Depends(get_db)
):
    """
    TBD_: Enhanced test endpoint to simulate a successful Stripe payment webhook.
    This is for testing purposes only and should be removed in production.
    """
    # Get the user from database
    user = db.query(User).filter(User.id == request.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Validate amount is in our allowed list
    if request.amount_cents not in PRICE_MAP:
        valid_amounts = list(PRICE_MAP.keys())
        raise HTTPException(
            status_code=400,
            detail=f"Invalid amount. Valid amounts are: {', '.join([f'{amt/100:.2f}' for amt in PRICE_MAP.keys()])} dollars"
        )

    # Convert amount from cents to USD
    amount_usd = Decimal(request.amount_cents) / 100

    # Update user balance
    old_balance = user.balance or Decimal("0")
    user.balance = old_balance + amount_usd

    # Create transaction record
    transaction = Transaction(
        user_id=user.id,
        amount=amount_usd,
        type="stripe_top_up",
        description=f"Test Stripe payment - ${amount_usd} (Session: {request.session_id})"
    )

    db.add(user)
    db.add(transaction)
    db.commit()
    db.refresh(transaction)

    return {
        "message": f"Test payment processed for user {request.user_id}: ${amount_usd}",
        "old_balance": float(old_balance),
        "new_balance": float(user.balance),
        "transaction_id": transaction.id,
        "session_id": request.session_id
    }

@router.post("/TBD_simulate-checkout-flow")
async def TBD_simulate_checkout_flow(
    request: CheckoutSessionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    TBD_: Simulate the entire Stripe checkout flow for testing.
    This creates a mock session and immediately processes the payment.
    """
    settings = get_settings()

    # Validate amount against predefined mapping
    if request.amount_cents not in PRICE_MAP:
        valid_amounts = list(PRICE_MAP.keys())
        raise HTTPException(
            status_code=400,
            detail=f"Invalid amount. Valid amounts are: {', '.join([f'{amt/100:.2f}' for amt in PRICE_MAP.keys()])} dollars"
        )

    # Create a mock session ID
    mock_session_id = f"cs_test_{current_user.id}_{request.amount_cents}_{int(time.time())}"

    # Process the payment immediately (simulate webhook)
    webhook_request = TBD_TestWebhookRequest(
        user_id=current_user.id,
        amount_cents=request.amount_cents,
        session_id=mock_session_id
    )

    result = await TBD_test_stripe_webhook_v2(webhook_request, db)

    return {
        "checkout_session_id": mock_session_id,
        "success_url": f"{settings.FRONTEND_BASE_URL}/success?session_id={mock_session_id}&test_mode=true",
        "payment_result": result
    }
