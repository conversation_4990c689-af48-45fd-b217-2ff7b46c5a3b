{#- 
  Base Configuration Template for Komodo Deployment
  
  This template generates configuration for different packages:
  - Pangolin: Basic pangolin deployment
  - Pangolin+: Pangolin with additional services (CrowdSec, middleware, etc.)
  - Pangolin+AI: Pangolin+ with AI services (Komodo, MCP Auth, NLWeb)
  - Coolify: Basic Coolify deployment
  - Coolify+: Coolify with CrowdSec integration
-#}

{#- Define package groups for cleaner conditionals -#}
{% set is_coolify = package in ["Coolify", "Coolify+"] %}
{% set is_pangolin = package in ["Pangolin", "Pangolin+", "Pangolin+AI"] %}
{% set has_crowdsec = package in ["Pangolin+", "Pangolin+AI", "Coolify+"] %}
{% set has_ai_services = package == "Pangolin+AI" %}
{% set has_extended_services = package in ["Pangolin+", "Pangolin+AI"] %}

{#- Define MAX_BACKUPS based on support level -#}
{% if support_level == "Level 1" %}
{% set max_backups = 0 %}
{% elif support_level == "Level 2" %}
{% set max_backups = 7 %}
{% elif support_level == "Level 3" %}
{% set max_backups = 28 %}
{% else %}
{% set max_backups = 0 %}
{% endif %}

{#- Default paths (can be overridden by provider templates) -#}
{% set coolify_custom_domain_path = coolify_custom_domain_path or "./config/coolify/proxy/dynamic/custom_domain.yml" %}

{#- Setup Stack Configuration -#}
[[stack]]
name = "{{ client_name_lower }}_setup-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
repo = "{{ github_repo }}"
reclone = true
file_paths = ["docker-compose-setup.yml"]
environment = """
DOMAIN={{ domain }}
{% if is_coolify %}
ADMIN_SUBDOMAIN={{ admin_subdomain or "coolify" }}
{% else %}
EMAIL={{ admin_email }}
ADMIN_USERNAME={{ admin_username }}
ADMIN_PASSWORD={{ admin_password }}
ADMIN_SUBDOMAIN={{ admin_subdomain }}
{% endif %}
{%- if is_pangolin %}
{%- if traefik_subdomain %}
TRAEFIK_SUBDOMAIN={{ traefik_subdomain }}
{%- endif %}
{%- if middleware_manager_subdomain %}
MIDDLEWARE_MANAGER_SUBDOMAIN={{ middleware_manager_subdomain }}
{%- endif %}
{%- if nlweb_subdomain %}
NLWEB_SUBDOMAIN={{ nlweb_subdomain }}
{%- endif %}
{%- if logs_subdomain %}
LOGS_SUBDOMAIN={{ logs_subdomain }}
{%- endif %}
{%- endif %}
{%- if package == "Pangolin" %}
COMPONENTS="pangolin"
SETUP_TOKEN={{ setup_token }}
{%- elif package == "Pangolin+" %}
COMPONENTS="pangolin+,crowdsec,middleware-manager,static-page,traefik-log-dashboard"
SETUP_TOKEN={{ setup_token }}
{%- elif package == "Pangolin+AI" %}
COMPONENTS="pangolin+,crowdsec,middleware-manager,komodo,mcpauth,nlweb,static-page,traefik-log-dashboard"
SETUP_TOKEN={{ setup_token }}
{%- elif package == "Coolify" %}
COMPONENTS="coolify"
{%- elif package == "Coolify+" %}
COMPONENTS="coolify+"
{%- endif %}
{%- if has_extended_services %}
CROWDSEC_ENROLLMENT_KEY={{ crowdsec_enrollment_key }}
STATIC_PAGE_SUBDOMAIN={{ static_page_subdomain }}
MAXMIND_LICENSE_KEY={{ maxmind_license_key }}
{%- elif package == "Coolify+" %}
CROWDSEC_ENROLLMENT_KEY={{ crowdsec_enrollment_key }}
{%- endif %}
{%- if has_ai_services %}
{%- if oauth_client_id %}
CLIENT_ID={{ oauth_client_id }}
{%- endif %}
{%- if oauth_client_secret %}
CLIENT_SECRET={{ oauth_client_secret }}
{%- endif %}
KOMODO_HOST_IP={{ komodo_host_ip }}
KOMODO_PASSKEY={{ komodo_passkey }}
OPENAI_API_KEY={{ openai_api_key }}
{%- endif %}
MAX_BACKUPS={{ max_backups }}
"""

{#- Main Stack Configuration -#}
{% if package == "Coolify" %}
[[stack]]
name = "{{ client_name_lower }}_main-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/{{ client_name_lower }}_setup-stack"
pre_deploy.command = """
# Add multiple commands on new lines. Supports comments.
cp {{ coolify_custom_domain_path }} /data/coolify/proxy/dynamic/custom_domain.yml
"""

{% elif package == "Coolify+" %}
[[stack]]
name = "{{ client_name_lower }}_main-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/{{ client_name_lower }}_setup-stack"
pre_deploy.command = """
# Add multiple commands on new lines. Supports comments.
cp {{ coolify_custom_domain_path }} /data/coolify/proxy/dynamic/custom_domain.yml
cp ./config/coolify/proxy/docker-compose.override.yml /data/coolify/proxy/docker-compose.override.yml
"""
post_deploy.command = """
# Add multiple commands on new lines. Supports comments.
key=$(docker exec -i {{ client_name_lower }}_main-stack-crowdsec-1 cscli bouncers add traefik-bouncer 2>/dev/null | awk '/API key for/{getline; while($0 ~ /^$/){getline}; print $1}') && [ -n "$key" ] && sed -i "s|PASTE_YOUR_KEY_HERE|$key|" ./config/coolify/proxy/dynamic/crowdsec-plugin.yml
cp ./config/coolify/proxy/dynamic/crowdsec-plugin.yml /data/coolify/proxy/dynamic/crowdsec-plugin.yml
"""

{% else %}
[[stack]]
name = "{{ client_name_lower }}_main-stack"
[stack.config]
server = "server-{{ client_name_lower }}"
files_on_host = true
reclone = true
run_directory = "/etc/komodo/stacks/{{ client_name_lower }}_setup-stack"
{%- if package == "Pangolin" %}
post_deploy.command = """
# Add multiple commands on new lines. Supports comments.
docker exec {{ client_name_lower }}_main-stack-pangolin-1 node -e "const Database = require('better-sqlite3'); const db = new Database('/app/config/db/db.sqlite'); db.prepare('UPDATE setupTokens SET token = ? WHERE rowid = (SELECT rowid FROM setupTokens ORDER BY rowid LIMIT 1)').run('{{ setup_token }}');"
"""
{%- elif has_extended_services %}
post_deploy.command = """
# Add multiple commands on new lines. Supports comments.
docker exec {{ client_name_lower }}_main-stack-pangolin-1 pangctl set-admin-credentials --email "{{ admin_username }}"  --password "{{ admin_password }}"
chmod +x ./components/pangolin/initialize_sqlite.sh
./components/pangolin/initialize_sqlite.sh
chmod +x ./components/crowdsec/update-bouncer-post-install.sh
./components/crowdsec/update-bouncer-post-install.sh {{ client_name_lower }}_main-stack-crowdsec-1
"""
ignore_services = ["maxmind-updater"]
{%- endif %}
{% endif %}

{#- Procedures Configuration -#}
[[procedure]]
name = "{{ client_name }}_ProcedureApply"
description = "This procedure runs the initial setup that write out a compose file for the main stack deployment"

[[procedure.config.stage]]
name = "{{ client_name }}_Setup"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "{{ client_name_lower }}_setup-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Compose Write"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Destroy {{ client_name }}_Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "{{ client_name_lower }}_setup-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "{{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "DeployStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], enabled = true }
]

[[procedure]]
name = "{{ client_name }}_ProcedureRestart"

[[procedure.config.stage]]
name = "Stop {{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "StopStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], enabled = true }
]

[[procedure.config.stage]]
name = "Wait For Stack Stop"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Start {{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "StartStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], enabled = true }
]

{% if is_coolify %}
[[procedure.config.stage]]
name = "Wait For Stack Start"
enabled = true
executions = [
  { execution.type = "Sleep", execution.params.duration_ms = 10000, enabled = true }
]

[[procedure.config.stage]]
name = "Restart ivobrett-261053_Stack Containers"
enabled = true
executions = [
  { execution.type = "RestartAllContainers", execution.params.server = "server-{{ client_name_lower }}", enabled = true }
]
{% endif %}


[[procedure]]
name = "{{ client_name }}_ProcedureDestroy"

[[procedure.config.stage]]
name = "{{ client_name }}_Stack"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "{{ client_name_lower }}_main-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

[[procedure.config.stage]]
name = "{{ client_name }}_Setup"
enabled = true
executions = [
  { execution.type = "DestroyStack", execution.params.stack = "{{ client_name_lower }}_setup-stack", execution.params.services = [], execution.params.remove_orphans = false, enabled = true }
]

{# User Group Configuration #}
[[user_group]]
name = "{{ client_name }}_user_group"
permissions = [
  { target.type = "Server", target.id = "server-{{ client_name_lower }}", level = "Write", specific = ["Attach", "Inspect", "Logs", "Processes", "Terminal"] },
  { target.type = "Stack", target.id = "{{ client_name_lower }}_setup-stack", level = "Write", specific = ["Inspect", "Logs", "Terminal"] },
  { target.type = "Stack", target.id = "{{ client_name_lower }}_main-stack", level = "Write", specific = ["Inspect", "Logs", "Terminal"] }
]
